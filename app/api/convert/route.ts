import { NextRequest, NextResponse } from 'next/server';
import { getCurrencies } from '@/lib/currency-service';

// Force dynamic rendering
export const dynamic = 'force-dynamic';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const from = searchParams.get('from');
    const to = searchParams.get('to');
    const amount = parseFloat(searchParams.get('amount') || '1');

    if (!from || !to) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Both from and to currency parameters are required',
          data: null
        },
        { status: 400 }
      );
    }

    if (isNaN(amount) || amount <= 0) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Amount must be a positive number',
          data: null
        },
        { status: 400 }
      );
    }

    // If converting the same currency, return the same amount
    if (from === to) {
      return NextResponse.json({
        success: true,
        data: {
          from,
          to,
          amount,
          convertedAmount: amount,
          rate: 1,
          timestamp: new Date().toISOString()
        }
      });
    }

    // Get all currencies to find the exchange rates
    const currencies = await getCurrencies({ limit: 1000 });
    
    // Create a map of currency symbols to their rates
    const currencyMap = new Map();
    currencies.forEach(currency => {
      // Extract base and quote currencies from symbol (e.g., "USD/TRY" -> base: USD, quote: TRY)
      const parts = currency.symbol.split('/');
      if (parts.length === 2) {
        const [base, quote] = parts;
        const rate = parseFloat(currency.lastPrice.toString());
        
        // Store both directions
        currencyMap.set(`${base}/${quote}`, rate);
        currencyMap.set(`${quote}/${base}`, 1 / rate);
      }
    });

    // Try to find direct conversion rate
    let conversionRate = currencyMap.get(`${from}/${to}`);
    
    if (!conversionRate) {
      // Try reverse direction
      const reverseRate = currencyMap.get(`${to}/${from}`);
      if (reverseRate) {
        conversionRate = 1 / reverseRate;
      }
    }

    if (!conversionRate) {
      // Try conversion through TRY as intermediate currency
      const fromToTry = currencyMap.get(`${from}/TRY`);
      const toToTry = currencyMap.get(`${to}/TRY`);
      
      if (fromToTry && toToTry) {
        // Convert from -> TRY -> to
        conversionRate = fromToTry / toToTry;
      } else {
        // Try the reverse direction through TRY
        const tryToFrom = currencyMap.get(`TRY/${from}`);
        const tryToTo = currencyMap.get(`TRY/${to}`);
        
        if (tryToFrom && tryToTo) {
          conversionRate = tryToTo / tryToFrom;
        }
      }
    }

    if (!conversionRate) {
      return NextResponse.json(
        { 
          success: false, 
          error: `Conversion rate not available for ${from} to ${to}`,
          data: null
        },
        { status: 404 }
      );
    }

    const convertedAmount = amount * conversionRate;

    return NextResponse.json({
      success: true,
      data: {
        from,
        to,
        amount,
        convertedAmount: parseFloat(convertedAmount.toFixed(2)),
        rate: parseFloat(conversionRate.toFixed(8)),
        timestamp: new Date().toISOString()
      }
    });

  } catch (error) {
    console.error('Error in currency conversion:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to convert currency',
        data: null
      },
      { status: 500 }
    );
  }
}
