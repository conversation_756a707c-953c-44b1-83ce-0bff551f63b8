import { NextRequest, NextResponse } from 'next/server';
import { getCurrencies } from '@/lib/currency-service';

// Force dynamic rendering
export const dynamic = 'force-dynamic';

export async function GET(request: NextRequest) {
  try {
    // Get all currencies from the database
    const currencies = await getCurrencies({ limit: 1000 });
    
    // Extract unique currency codes from symbols
    const currencySet = new Set<string>();
    const currencyData = new Map<string, { name: string; flag: string; lastUpdated: Date | null }>();
    
    currencies.forEach(currency => {
      const parts = currency.symbol.split('/');
      if (parts.length === 2) {
        const [base, quote] = parts;
        
        // Add both currencies to the set
        currencySet.add(base);
        currencySet.add(quote);
        
        // Store additional data for each currency
        if (!currencyData.has(base)) {
          currencyData.set(base, {
            name: getCurrencyName(base),
            flag: getCurrencyFlag(base),
            lastUpdated: currency.lastUpdated
          });
        }
        
        if (!currencyData.has(quote)) {
          currencyData.set(quote, {
            name: getCurrencyName(quote),
            flag: getCurrencyFlag(quote),
            lastUpdated: currency.lastUpdated
          });
        }
      }
    });
    
    // Convert to array and sort
    const availableCurrencies = Array.from(currencySet)
      .map(code => ({
        code,
        name: currencyData.get(code)?.name || code,
        flag: currencyData.get(code)?.flag || '💱',
        lastUpdated: currencyData.get(code)?.lastUpdated
      }))
      .sort((a, b) => {
        // Sort TRY first, then alphabetically
        if (a.code === 'TRY') return -1;
        if (b.code === 'TRY') return 1;
        return a.code.localeCompare(b.code);
      });

    return NextResponse.json({
      success: true,
      data: availableCurrencies,
      total: availableCurrencies.length,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Error fetching converter currencies:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to fetch currencies',
        data: []
      },
      { status: 500 }
    );
  }
}

// Helper function to get currency names
function getCurrencyName(code: string): string {
  const currencyNames: { [key: string]: string } = {
    'TRY': 'Turkish Lira',
    'USD': 'US Dollar',
    'EUR': 'Euro',
    'GBP': 'British Pound',
    'JPY': 'Japanese Yen',
    'CHF': 'Swiss Franc',
    'CAD': 'Canadian Dollar',
    'AUD': 'Australian Dollar',
    'SEK': 'Swedish Krona',
    'NOK': 'Norwegian Krone',
    'DKK': 'Danish Krone',
    'PLN': 'Polish Zloty',
    'CZK': 'Czech Koruna',
    'HUF': 'Hungarian Forint',
    'RON': 'Romanian Leu',
    'BGN': 'Bulgarian Lev',
    'HRK': 'Croatian Kuna',
    'RSD': 'Serbian Dinar',
    'RUB': 'Russian Ruble',
    'UAH': 'Ukrainian Hryvnia',
    'BTC': 'Bitcoin',
    'ETH': 'Ethereum',
    'XAU': 'Gold',
    'XAG': 'Silver',
    'CNY': 'Chinese Yuan',
    'INR': 'Indian Rupee',
    'KRW': 'South Korean Won',
    'SGD': 'Singapore Dollar',
    'HKD': 'Hong Kong Dollar',
    'MXN': 'Mexican Peso',
    'BRL': 'Brazilian Real',
    'ARS': 'Argentine Peso',
    'CLP': 'Chilean Peso',
    'COP': 'Colombian Peso',
    'PEN': 'Peruvian Sol',
    'ZAR': 'South African Rand',
    'EGP': 'Egyptian Pound',
    'SAR': 'Saudi Riyal',
    'AED': 'UAE Dirham',
    'QAR': 'Qatari Riyal',
    'KWD': 'Kuwaiti Dinar',
    'BHD': 'Bahraini Dinar',
    'OMR': 'Omani Rial',
    'JOD': 'Jordanian Dinar',
    'LBP': 'Lebanese Pound',
    'ILS': 'Israeli Shekel',
    'IRR': 'Iranian Rial',
    'IQD': 'Iraqi Dinar',
    'SYP': 'Syrian Pound',
    'AFN': 'Afghan Afghani',
    'PKR': 'Pakistani Rupee',
    'BDT': 'Bangladeshi Taka',
    'LKR': 'Sri Lankan Rupee',
    'NPR': 'Nepalese Rupee',
    'BTN': 'Bhutanese Ngultrum',
    'MVR': 'Maldivian Rufiyaa',
    'THB': 'Thai Baht',
    'MYR': 'Malaysian Ringgit',
    'IDR': 'Indonesian Rupiah',
    'PHP': 'Philippine Peso',
    'VND': 'Vietnamese Dong',
    'KHR': 'Cambodian Riel',
    'LAK': 'Lao Kip',
    'MMK': 'Myanmar Kyat',
    'BND': 'Brunei Dollar',
    'TWD': 'Taiwan Dollar',
    'MOP': 'Macanese Pataca',
    'NZD': 'New Zealand Dollar',
    'FJD': 'Fijian Dollar',
    'PGK': 'Papua New Guinean Kina',
    'SBD': 'Solomon Islands Dollar',
    'VUV': 'Vanuatu Vatu',
    'WST': 'Samoan Tala',
    'TOP': 'Tongan Paʻanga',
    'XPF': 'CFP Franc'
  };
  
  return currencyNames[code] || code;
}

// Helper function to get currency flags
function getCurrencyFlag(code: string): string {
  const currencyFlags: { [key: string]: string } = {
    'TRY': '🇹🇷',
    'USD': '🇺🇸',
    'EUR': '🇪🇺',
    'GBP': '🇬🇧',
    'JPY': '🇯🇵',
    'CHF': '🇨🇭',
    'CAD': '🇨🇦',
    'AUD': '🇦🇺',
    'SEK': '🇸🇪',
    'NOK': '🇳🇴',
    'DKK': '🇩🇰',
    'PLN': '🇵🇱',
    'CZK': '🇨🇿',
    'HUF': '🇭🇺',
    'RON': '🇷🇴',
    'BGN': '🇧🇬',
    'HRK': '🇭🇷',
    'RSD': '🇷🇸',
    'RUB': '🇷🇺',
    'UAH': '🇺🇦',
    'BTC': '₿',
    'ETH': '⟠',
    'XAU': '🥇',
    'XAG': '🥈',
    'CNY': '🇨🇳',
    'INR': '🇮🇳',
    'KRW': '🇰🇷',
    'SGD': '🇸🇬',
    'HKD': '🇭🇰',
    'MXN': '🇲🇽',
    'BRL': '🇧🇷',
    'ARS': '🇦🇷',
    'CLP': '🇨🇱',
    'COP': '🇨🇴',
    'PEN': '🇵🇪',
    'ZAR': '🇿🇦',
    'EGP': '🇪🇬',
    'SAR': '🇸🇦',
    'AED': '🇦🇪',
    'QAR': '🇶🇦',
    'KWD': '🇰🇼',
    'BHD': '🇧🇭',
    'OMR': '🇴🇲',
    'JOD': '🇯🇴',
    'LBP': '🇱🇧',
    'ILS': '🇮🇱',
    'IRR': '🇮🇷',
    'IQD': '🇮🇶',
    'SYP': '🇸🇾',
    'AFN': '🇦🇫',
    'PKR': '🇵🇰',
    'BDT': '🇧🇩',
    'LKR': '🇱🇰',
    'NPR': '🇳🇵',
    'BTN': '🇧🇹',
    'MVR': '🇲🇻',
    'THB': '🇹🇭',
    'MYR': '🇲🇾',
    'IDR': '🇮🇩',
    'PHP': '🇵🇭',
    'VND': '🇻🇳',
    'KHR': '🇰🇭',
    'LAK': '🇱🇦',
    'MMK': '🇲🇲',
    'BND': '🇧🇳',
    'TWD': '🇹🇼',
    'MOP': '🇲🇴',
    'NZD': '🇳🇿',
    'FJD': '🇫🇯',
    'PGK': '🇵🇬',
    'SBD': '🇸🇧',
    'VUV': '🇻🇺',
    'WST': '🇼🇸',
    'TOP': '🇹🇴',
    'XPF': '🇵🇫'
  };
  
  return currencyFlags[code] || '💱';
}
