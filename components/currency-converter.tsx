"use client"

import { useState, useEffect, useCallback, useRef } from "react"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { ArrowUpDown, Loader2, Search, Check, ChevronsUpDown } from "lucide-react"
import { useLanguage } from "./language-provider"

import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { cn } from "@/lib/utils"

interface Currency {
  code: string;
  name: string;
  flag: string;
  lastUpdated: string | null;
}

interface ConversionResult {
  from: string;
  to: string;
  amount: number;
  convertedAmount: number;
  rate: number;
  timestamp: string;
}

export function CurrencyConverter() {
  const [fromCurrency, setFromCurrency] = useState("TRY")
  const [toCurrency, setToCurrency] = useState("USD")
  const [fromAmount, setFromAmount] = useState("100")
  const [toAmount, setToAmount] = useState("")
  const [currencies, setCurrencies] = useState<Currency[]>([])
  const [loading, setLoading] = useState(true)
  const [converting, setConverting] = useState(false)
  const [currentRate, setCurrentRate] = useState<number | null>(null)
  const [lastUpdated, setLastUpdated] = useState<string | null>(null)
  const [fromOpen, setFromOpen] = useState(false)
  const [toOpen, setToOpen] = useState(false)
  const debounceTimer = useRef<NodeJS.Timeout | null>(null)
  const { t } = useLanguage()

  // Fetch available currencies
  useEffect(() => {
    const fetchCurrencies = async () => {
      try {
        setLoading(true)
        const response = await fetch('/api/converter/currencies')
        const data = await response.json()

        if (data.success) {
          setCurrencies(data.data)
        } else {
          console.error('Failed to fetch currencies:', data.error)
        }
      } catch (error) {
        console.error('Error fetching currencies:', error)
      } finally {
        setLoading(false)
      }
    }

    fetchCurrencies()
  }, [])

  // Debug log
  useEffect(() => {
    console.log('Currencies loaded:', currencies.length)
  }, [currencies])

  // Convert currency using API
  const convertCurrency = async (amount: string, from: string, to: string) => {
    if (!amount || amount === "0" || from === to) {
      return from === to ? amount : "0"
    }

    try {
      setConverting(true)
      const response = await fetch(`/api/convert?from=${from}&to=${to}&amount=${amount}`)
      const data = await response.json()

      if (data.success) {
        setCurrentRate(data.data.rate)
        setLastUpdated(data.data.timestamp)
        // Format to 2 decimal places for display
        return parseFloat(data.data.convertedAmount).toFixed(2)
      } else {
        console.error('Conversion failed:', data.error)
        return "0"
      }
    } catch (error) {
      console.error('Error converting currency:', error)
      return "0"
    } finally {
      setConverting(false)
    }
  }

  // Handle conversion when inputs change with debouncing
  useEffect(() => {
    if (!fromAmount || !fromCurrency || !toCurrency || loading) {
      return
    }

    if (debounceTimer.current) {
      clearTimeout(debounceTimer.current)
    }

    debounceTimer.current = setTimeout(() => {
      convertCurrency(fromAmount, fromCurrency, toCurrency).then(setToAmount)
    }, 700) // 0.7 seconds delay

    return () => {
      if (debounceTimer.current) {
        clearTimeout(debounceTimer.current)
      }
    }
  }, [fromAmount, fromCurrency, toCurrency, loading])

  // Cleanup timer on unmount
  useEffect(() => {
    return () => {
      if (debounceTimer.current) {
        clearTimeout(debounceTimer.current)
      }
    }
  }, [])

  const handleFromAmountChange = (value: string) => {
    setFromAmount(value)
  }

  const handleToAmountChange = (value: string) => {
    setToAmount(value)
    // For reverse conversion, we'll handle it separately to avoid conflicts
    if (value && toCurrency && fromCurrency && !loading) {
      if (debounceTimer.current) {
        clearTimeout(debounceTimer.current)
      }

      debounceTimer.current = setTimeout(() => {
        convertCurrency(value, toCurrency, fromCurrency).then(setFromAmount)
      }, 700)
    }
  }

  const swapCurrencies = () => {
    const tempCurrency = fromCurrency
    const tempAmount = fromAmount

    setFromCurrency(toCurrency)
    setToCurrency(tempCurrency)
    setFromAmount(toAmount)
    setToAmount(tempAmount)
  }

  // Currency selector component
  const CurrencySelector = ({
    value,
    onValueChange,
    open,
    onOpenChange,
    placeholder = "Select currency..."
  }: {
    value: string
    onValueChange: (value: string) => void
    open: boolean
    onOpenChange: (open: boolean) => void
    placeholder?: string
  }) => {
    const [searchTerm, setSearchTerm] = useState("")
    const selectedCurrency = currencies.find(currency => currency.code === value)

    // Filter currencies based on search term
    const filteredCurrencies = currencies.filter(currency =>
      currency.code.toLowerCase().includes(searchTerm.toLowerCase()) ||
      currency.name.toLowerCase().includes(searchTerm.toLowerCase())
    )

    return (
      <Popover open={open} onOpenChange={onOpenChange}>
        <PopoverTrigger asChild>
          <Button
            variant="outline"
            role="combobox"
            aria-expanded={open}
            className="w-40 justify-between bg-background/50 border-border text-foreground"
            disabled={converting}
          >
            {selectedCurrency ? (
              <div className="flex items-center space-x-2">
                <span>{selectedCurrency.flag}</span>
                <span className="font-medium">{selectedCurrency.code}</span>
              </div>
            ) : (
              placeholder
            )}
            <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-80 p-0" align="start">
          <div className="flex flex-col">
            {/* Search Input */}
            <div className="flex items-center border-b px-3">
              <Search className="mr-2 h-4 w-4 shrink-0 opacity-50" />
              <Input
                placeholder="Search currency..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="h-11 border-0 bg-transparent focus:ring-0 focus:ring-offset-0"
              />
            </div>

            {/* Currency List */}
            <div className="max-h-60 overflow-y-auto p-1">
              {filteredCurrencies.length === 0 ? (
                <div className="py-6 text-center text-sm text-muted-foreground">
                  No currency found.
                </div>
              ) : (
                filteredCurrencies.map((currency) => {
                  const handleCurrencySelect = () => {
                    console.log('Currency selected:', currency.code)
                    onValueChange(currency.code)
                    onOpenChange(false)
                    setSearchTerm("") // Reset search
                  }

                  return (
                    <div
                      key={currency.code}
                      onClick={handleCurrencySelect}
                      className="relative flex cursor-pointer select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground"
                    >
                      <div className="flex items-center space-x-2 flex-1">
                        <span>{currency.flag}</span>
                        <span className="font-medium">{currency.code}</span>
                        <span className="text-xs text-muted-foreground truncate">{currency.name}</span>
                      </div>
                      <Check
                        className={cn(
                          "ml-auto h-4 w-4",
                          value === currency.code ? "opacity-100" : "opacity-0"
                        )}
                      />
                    </div>
                  )
                })
              )}
            </div>
          </div>
        </PopoverContent>
      </Popover>
    )
  }

  return (
    <section id="converter" className="space-y-6">
      <div className="text-center">
        <h2 className="text-2xl font-bold text-foreground mb-2 drop-shadow-lg">{t("currencyConverter")}</h2>
        <p className="text-muted-foreground">{t("convertDescription")}</p>
      </div>

      <Card className="max-w-2xl mx-auto bg-card/80 backdrop-blur-sm border-border hover:border-primary/50 transition-all duration-300">
        <CardHeader>
          <CardTitle className="text-center text-foreground">{t("quickConvert")}</CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          {loading ? (
            <div className="flex items-center justify-center py-8">
              <Loader2 className="h-8 w-8 animate-spin text-primary" />
              <span className="ml-2 text-muted-foreground">Loading currencies...</span>
            </div>
          ) : (
            <>
              {/* From Currency */}
              <div className="space-y-2">
                <Label htmlFor="from-amount" className="text-muted-foreground">
                  {t("from")}
                </Label>
                <div className="flex space-x-2">
                  <div className="flex-1">
                    <Input
                      id="from-amount"
                      type="number"
                      value={fromAmount}
                      onChange={(e) => handleFromAmountChange(e.target.value)}
                      placeholder={t("enterAmount")}
                      className="text-lg bg-background/50 border-border text-foreground placeholder:text-muted-foreground focus:border-primary focus:ring-primary/50"
                      disabled={converting}
                    />
                  </div>
                  <CurrencySelector
                    value={fromCurrency}
                    onValueChange={setFromCurrency}
                    open={fromOpen}
                    onOpenChange={setFromOpen}
                    placeholder="From currency..."
                  />
                </div>
              </div>

              {/* Swap Button */}
              <div className="flex justify-center">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={swapCurrencies}
                  className="rounded-full p-2 bg-primary border-primary text-primary-foreground hover:bg-primary/80 shadow-lg shadow-primary/30"
                  disabled={converting}
                >
                  <ArrowUpDown className="h-4 w-4" />
                </Button>
              </div>

              {/* To Currency */}
              <div className="space-y-2">
                <Label htmlFor="to-amount" className="text-muted-foreground">
                  {t("to")}
                </Label>
                <div className="flex space-x-2">
                  <div className="flex-1 relative">
                    <Input
                      id="to-amount"
                      type="number"
                      value={toAmount}
                      onChange={(e) => handleToAmountChange(e.target.value)}
                      placeholder={t("convertedAmount")}
                      className="text-lg bg-background/50 border-border text-foreground placeholder:text-muted-foreground focus:border-primary focus:ring-primary/50"
                      disabled={converting}
                    />
                    {converting && (
                      <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
                        <Loader2 className="h-4 w-4 animate-spin text-primary" />
                      </div>
                    )}
                  </div>
                  <CurrencySelector
                    value={toCurrency}
                    onValueChange={setToCurrency}
                    open={toOpen}
                    onOpenChange={setToOpen}
                    placeholder="To currency..."
                  />
                </div>
              </div>

              {/* Exchange Rate Info */}
              <div className="bg-accent/50 rounded-lg p-4 text-center border border-border">
                {currentRate ? (
                  <>
                    <p className="text-sm text-muted-foreground">
                      1 {fromCurrency} = {currentRate.toFixed(4)} {toCurrency}
                    </p>
                    {lastUpdated && (
                      <p className="text-xs text-muted-foreground/70 mt-1">
                        {t("midMarketRate")} • Updated: {new Date(lastUpdated).toLocaleTimeString()}
                      </p>
                    )}
                  </>
                ) : (
                  <p className="text-sm text-muted-foreground">
                    {converting ? "Calculating rate..." : "Enter amount to see exchange rate"}
                  </p>
                )}
              </div>
            </>
          )}
        </CardContent>
      </Card>
    </section>
  )
}
