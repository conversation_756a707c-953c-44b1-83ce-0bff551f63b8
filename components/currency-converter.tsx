"use client"

import { useState, useEffect } from "react"
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Button } from "@/components/ui/button"
import { ArrowUpDown, Loader2 } from "lucide-react"
import { useLanguage } from "./language-provider"

interface Currency {
  code: string;
  name: string;
  flag: string;
  lastUpdated: string | null;
}

interface ConversionResult {
  from: string;
  to: string;
  amount: number;
  convertedAmount: number;
  rate: number;
  timestamp: string;
}

export function CurrencyConverter() {
  const [fromCurrency, setFromCurrency] = useState("TRY")
  const [toCurrency, setToCurrency] = useState("USD")
  const [fromAmount, setFromAmount] = useState("100")
  const [to<PERSON><PERSON>, setToAmount] = useState("")
  const [currencies, setCurrencies] = useState<Currency[]>([])
  const [loading, setLoading] = useState(true)
  const [converting, setConverting] = useState(false)
  const [currentRate, setCurrentRate] = useState<number | null>(null)
  const [lastUpdated, setLastUpdated] = useState<string | null>(null)
  const { t } = useLanguage()

  // Fetch available currencies
  useEffect(() => {
    const fetchCurrencies = async () => {
      try {
        setLoading(true)
        const response = await fetch('/api/converter/currencies')
        const data = await response.json()

        if (data.success) {
          setCurrencies(data.data)
        } else {
          console.error('Failed to fetch currencies:', data.error)
        }
      } catch (error) {
        console.error('Error fetching currencies:', error)
      } finally {
        setLoading(false)
      }
    }

    fetchCurrencies()
  }, [])

  // Convert currency using API
  const convertCurrency = async (amount: string, from: string, to: string) => {
    if (!amount || amount === "0" || from === to) {
      return from === to ? amount : "0"
    }

    try {
      setConverting(true)
      const response = await fetch(`/api/convert?from=${from}&to=${to}&amount=${amount}`)
      const data = await response.json()

      if (data.success) {
        setCurrentRate(data.data.rate)
        setLastUpdated(data.data.timestamp)
        // Format to 2 decimal places for display
        return parseFloat(data.data.convertedAmount).toFixed(2)
      } else {
        console.error('Conversion failed:', data.error)
        return "0"
      }
    } catch (error) {
      console.error('Error converting currency:', error)
      return "0"
    } finally {
      setConverting(false)
    }
  }

  // Handle conversion when inputs change
  useEffect(() => {
    if (fromAmount && fromCurrency && toCurrency && !loading) {
      convertCurrency(fromAmount, fromCurrency, toCurrency).then(setToAmount)
    }
  }, [fromAmount, fromCurrency, toCurrency, loading])

  const handleFromAmountChange = (value: string) => {
    setFromAmount(value)
  }

  const handleToAmountChange = (value: string) => {
    setToAmount(value)
    if (value && toCurrency && fromCurrency) {
      convertCurrency(value, toCurrency, fromCurrency).then(setFromAmount)
    }
  }

  const swapCurrencies = () => {
    const tempCurrency = fromCurrency
    const tempAmount = fromAmount

    setFromCurrency(toCurrency)
    setToCurrency(tempCurrency)
    setFromAmount(toAmount)
    setToAmount(tempAmount)
  }

  return (
    <section id="converter" className="space-y-6">
      <div className="text-center">
        <h2 className="text-2xl font-bold text-foreground mb-2 drop-shadow-lg">{t("currencyConverter")}</h2>
        <p className="text-muted-foreground">{t("convertDescription")}</p>
      </div>

      <Card className="max-w-2xl mx-auto bg-card/80 backdrop-blur-sm border-border hover:border-primary/50 transition-all duration-300">
        <CardHeader>
          <CardTitle className="text-center text-foreground">{t("quickConvert")}</CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          {loading ? (
            <div className="flex items-center justify-center py-8">
              <Loader2 className="h-8 w-8 animate-spin text-primary" />
              <span className="ml-2 text-muted-foreground">Loading currencies...</span>
            </div>
          ) : (
            <>
              {/* From Currency */}
              <div className="space-y-2">
                <Label htmlFor="from-amount" className="text-muted-foreground">
                  {t("from")}
                </Label>
                <div className="flex space-x-2">
                  <div className="flex-1">
                    <Input
                      id="from-amount"
                      type="number"
                      value={fromAmount}
                      onChange={(e) => handleFromAmountChange(e.target.value)}
                      placeholder={t("enterAmount")}
                      className="text-lg bg-background/50 border-border text-foreground placeholder:text-muted-foreground focus:border-primary focus:ring-primary/50"
                      disabled={converting}
                    />
                  </div>
                  <Select value={fromCurrency} onValueChange={setFromCurrency} disabled={converting}>
                    <SelectTrigger className="w-40 bg-background/50 border-border text-foreground">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent className="bg-card border-border max-h-60 overflow-y-auto">
                      {currencies.map((currency) => (
                        <SelectItem key={currency.code} value={currency.code} className="text-foreground hover:bg-accent">
                          <div className="flex items-center space-x-2">
                            <span>{currency.flag}</span>
                            <span className="font-medium">{currency.code}</span>
                            <span className="text-xs text-muted-foreground truncate">{currency.name}</span>
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>

              {/* Swap Button */}
              <div className="flex justify-center">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={swapCurrencies}
                  className="rounded-full p-2 bg-primary border-primary text-primary-foreground hover:bg-primary/80 shadow-lg shadow-primary/30"
                  disabled={converting}
                >
                  <ArrowUpDown className="h-4 w-4" />
                </Button>
              </div>

              {/* To Currency */}
              <div className="space-y-2">
                <Label htmlFor="to-amount" className="text-muted-foreground">
                  {t("to")}
                </Label>
                <div className="flex space-x-2">
                  <div className="flex-1 relative">
                    <Input
                      id="to-amount"
                      type="number"
                      value={toAmount}
                      onChange={(e) => handleToAmountChange(e.target.value)}
                      placeholder={t("convertedAmount")}
                      className="text-lg bg-background/50 border-border text-foreground placeholder:text-muted-foreground focus:border-primary focus:ring-primary/50"
                      disabled={converting}
                    />
                    {converting && (
                      <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
                        <Loader2 className="h-4 w-4 animate-spin text-primary" />
                      </div>
                    )}
                  </div>
                  <Select value={toCurrency} onValueChange={setToCurrency} disabled={converting}>
                    <SelectTrigger className="w-40 bg-background/50 border-border text-foreground">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent className="bg-card border-border max-h-60 overflow-y-auto">
                      {currencies.map((currency) => (
                        <SelectItem key={currency.code} value={currency.code} className="text-foreground hover:bg-accent">
                          <div className="flex items-center space-x-2">
                            <span>{currency.flag}</span>
                            <span className="font-medium">{currency.code}</span>
                            <span className="text-xs text-muted-foreground truncate">{currency.name}</span>
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>

              {/* Exchange Rate Info */}
              <div className="bg-accent/50 rounded-lg p-4 text-center border border-border">
                {currentRate ? (
                  <>
                    <p className="text-sm text-muted-foreground">
                      1 {fromCurrency} = {currentRate.toFixed(4)} {toCurrency}
                    </p>
                    {lastUpdated && (
                      <p className="text-xs text-muted-foreground/70 mt-1">
                        {t("midMarketRate")} • Updated: {new Date(lastUpdated).toLocaleTimeString()}
                      </p>
                    )}
                  </>
                ) : (
                  <p className="text-sm text-muted-foreground">
                    {converting ? "Calculating rate..." : "Enter amount to see exchange rate"}
                  </p>
                )}
              </div>
            </>
          )}
        </CardContent>
      </Card>
    </section>
  )
}
