"use client"

import { useLanguage } from "./language-provider"

export function CurrencyFooter() {
  const { t } = useLanguage()

  return (
    <footer className="bg-gradient-to-r from-slate-900/90 to-slate-800/90 border-t border-cyan-500/30 mt-16">
      <div className="container mx-auto px-4 py-8">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          {/* Brand */}
          <div className="space-y-4">
            <div className="flex items-center space-x-2">
              <div className="w-6 h-6 bg-gradient-to-br from-cyan-400 to-cyan-600 rounded flex items-center justify-center shadow-lg shadow-cyan-500/50">
                <span className="text-white font-bold text-xs">₺</span>
              </div>
              <span className="font-bold text-lg text-white">{t("brandName")}</span>
            </div>
            <p className="text-sm text-cyan-200 text-pretty">
              {t("brandDescription")}
            </p>
          </div>

          {/* Quick Links */}
          <div className="space-y-4">
            <h3 className="font-semibold text-white">{t("quickLinks")}</h3>
            <nav className="flex flex-col space-y-2">
              <a href="#rates" className="text-sm text-cyan-200 hover:text-cyan-300 transition-colors">
                {t("liveRates")}
              </a>
              <a href="#converter" className="text-sm text-cyan-200 hover:text-cyan-300 transition-colors">
                {t("currencyConverterLink")}
              </a>
              <a href="#" className="text-sm text-cyan-200 hover:text-cyan-300 transition-colors">
                {t("historicalData")}
              </a>
              <a href="#" className="text-sm text-cyan-200 hover:text-cyan-300 transition-colors">
                {t("apiAccess")}
              </a>
            </nav>
          </div>

          {/* Legal */}
          <div className="space-y-4">
            <h3 className="font-semibold text-white">{t("legal")}</h3>
            <nav className="flex flex-col space-y-2">
              <a href="#" className="text-sm text-cyan-200 hover:text-cyan-300 transition-colors">
                {t("termsOfService")}
              </a>
              <a href="#" className="text-sm text-cyan-200 hover:text-cyan-300 transition-colors">
                {t("privacyPolicy")}
              </a>
              <a href="#" className="text-sm text-cyan-200 hover:text-cyan-300 transition-colors">
                {t("contactUs")}
              </a>
            </nav>
          </div>
        </div>

        <div className="border-t border-cyan-500/30 mt-8 pt-6 text-center">
          <p className="text-sm text-cyan-200">
            {t("copyright")}
          </p>
        </div>
      </div>
    </footer>
  )
}
